AWSTemplateFormatVersion: '2010-09-09'
Transform: 'AWS::Serverless-2016-10-31'
Description: API Lambda to send events to Event Bus
Globals:
  Function:
    Timeout: 300
Parameters:
  AWSRegion:
    Type: String
    Description: AWS Region
    Default: us-east-1
  StackPrefix:
    Type: String
    Default: nova-offer-state-writer
    Description: Name of CloudFormation stack prefix
  Environment:
    Type: String
    Description: Environment Name
    Default: dev
  KMSKeyName:
    Description: The name of the KMS Key
    Type: String
    Default: amrpwl-nonprod-application
  EventBusStackPrefix:
    Description: The prefix of Event Bus Stack
    Type: String
  ErroralarmThresholdProducer:
    Type: Number
    Description: Checks if the lambda invocation encountered an error
  DurationalarmThresholdProducer:
    Type: Number
    Description: Checks the duration of lambda invocation
  ThrottlealarmThresholdProducer:
    Type: Number
    Description: The percentage threshold at which a throttle alarm is triggered
  PagerDutyURLOfferStateProducer:
    Type: String
    Description: Pager Duty endpoint
  KinesisForSplunkStackName:
    Description: The name of Kinesis which is unique for each environment, used for logging
      to splunk
    Type: String
Conditions:
  IsDevEnvironment:
    Fn::Equals:
      - Ref: Environment
      - dev
      
Mappings:
  AssignedReservedConcurrency:
    sole:
      reserved: 10
    dev:
      reserved: 10
    int:
      reserved: 10
    uat:
      reserved: 20
    prod:
      reserved: 20

Resources:
  StateProducerLambda:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName:
        Fn::Sub: '${AWS::StackName}'
      Description: Lambda function to connect API Call to Kinesis
      Tracing: PassThrough
      CodeUri: PLACEHOLDER
      Handler: com.loyalty.nova.offer.state.writer.producer.StreamLambdaHandler
      Runtime: java17
      MemorySize: 3008
      ReservedConcurrentExecutions:
        Fn::FindInMap:
          - AssignedReservedConcurrency
          - Ref: Environment
          - "reserved"
      Role:
        Fn::GetAtt: [LambdaPermissions, Arn]
      Environment:
        Variables:
          KINESIS:
            Fn::ImportValue:
              Fn::Sub: '${Environment}-${EventBusStackPrefix}-Kinesis-ARN'
          KINESIS_NAME:
            Fn::ImportValue:
              Fn::Sub: '${Environment}-${EventBusStackPrefix}-Kinesis-Name'
          KINESIS_REGION:
            Ref: AWSRegion
          SPRING_PROFILES_ACTIVE:
            Ref: Environment
  LambdaPermissions:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: 'Allow'
            Principal:
              Service:
                - 'lambda.amazonaws.com'
            Action:
              - 'sts:AssumeRole'
      RoleName:
        Fn::Sub: '${AWS::StackName}-permissions'
      Policies:
        - PolicyName:
            Fn::Sub: '${AWS::StackName}-StreamPermissions'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - kinesis:DescribeStream
                  - kinesis:PutRecord
                Resource:
                  Fn::ImportValue:
                    Fn::Sub: '${Environment}-${EventBusStackPrefix}-Kinesis-ARN'
        - PolicyName:
            Fn::Sub: '${AWS::StackName}-Logs'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - logs:PutLogEvents
                  - logs:CreateLogStream
                Resource:
                  Fn::GetAtt: [LogGroup, Arn]
        - PolicyName:
            Fn::Sub: '${AWS::StackName}-Kinesis'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - kinesis:*
                Resource:
                  Fn::ImportValue:
                    Fn::Sub: '${Environment}-${EventBusStackPrefix}-Kinesis-ARN'
        - PolicyName:
            Fn::Sub: '${AWS::StackName}-KMS'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Resource:
                  - Fn::ImportValue:
                      Ref:
                        KMSKeyName
                Action:
                  - kms:GenerateDataKey
  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName:
        Fn::Sub: '/aws/lambda/${AWS::StackName}'
      RetentionInDays: 14
  SubscriptionFilter:
    DependsOn: LogGroup
    Type: 'AWS::Logs::SubscriptionFilter'
    Properties:
      RoleArn:
        Fn::ImportValue:
          Fn::Sub: '${KinesisForSplunkStackName}-Role-Arn'
      LogGroupName:
        Ref: LogGroup
      FilterPattern: ''
      DestinationArn:
        Fn::ImportValue:
          Fn::Sub: '${KinesisForSplunkStackName}-Stream-Arn'
  AlarmSNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      Subscription:
        - Endpoint:
            Fn::Sub: '${PagerDutyURLOfferStateProducer}'
          Protocol: https
      TopicName:
        Fn::Sub: '${AWS::StackName}-SNS-Alarm'
  ErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: Checks if the lambda invocation encountered an error
      AlarmActions:
        - Ref: 'AlarmSNSTopic'
      OKActions:
        - Ref: 'AlarmSNSTopic'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Maximum
      Period: '60'
      EvaluationPeriods: '1'
      Threshold:
         Ref: ErroralarmThresholdProducer
      ActionsEnabled:
         Fn::If:
           - IsDevEnvironment
           - false
           - true
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: FunctionName
          Value:
            Ref: StateProducerLambda
  DurationAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: Checks the duration of lambda invocation
      AlarmActions:
        - Ref: 'AlarmSNSTopic'
      OKActions:
        - Ref: 'AlarmSNSTopic'
      MetricName: Duration
      Namespace: AWS/Lambda
      Statistic: Maximum
      Period: '60'
      EvaluationPeriods: '1'
      Threshold:
         Ref: DurationalarmThresholdProducer
      ActionsEnabled:
         Fn::If:
           - IsDevEnvironment
           - false
           - true
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: FunctionName
          Value:
            Ref: StateProducerLambda
  ThrottleAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: "Checks if the Lambda function is being throttled"
      AlarmActions:
        - Ref: 'AlarmSNSTopic'
      OKActions:
        - Ref: 'AlarmSNSTopic'
      MetricName: Throttles
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: '60'
      EvaluationPeriods: '1'
      Threshold:
         Ref: ThrottlealarmThresholdProducer
      ActionsEnabled:
         Fn::If:
            - IsDevEnvironment
            - false
            - true
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: FunctionName
          Value:
            Ref: StateProducerLambda
Outputs:
  LogGroup:
    Description: The name of the log group created for the app
    Value:
      Ref: LogGroup
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-LogGroupName'
  Lambda:
    Description: Lambda function
    Value:
      Ref: StateProducerLambda
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-Lambda'
  LambdaArn:
    Description: Offer State Producer Lambda ARN
    Value:
      Fn::GetAtt: [StateProducerLambda, Arn]
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-api-ARN'
