package com.loyalty.nova.offer.state.writer.producer

import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.v3.StateChangedEventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Profile("local")
@Service
class EventProducerServiceLocalImpl : EventProducerService {

    override fun publishEvent(event: Event<StateChangedEventData, EventMeta>) {
        println("<===== publishEvent $event")
    }

}
